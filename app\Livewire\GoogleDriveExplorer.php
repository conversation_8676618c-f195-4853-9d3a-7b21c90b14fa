<?php

namespace App\Livewire;

use Livewire\Component;
use App\Services\GoogleDriveService;
use LBCDev\OAuthManager\Models\OAuthService;

class GoogleDriveExplorer extends Component
{
    public $oauthServiceId;
    public $currentFolderId = 'root';
    public $files = [];
    public $breadcrumb = [];
    public $selectedFile = null;
    public $selectedFileUrl = '';
    public $searchQuery = '';
    public $isLoading = false;
    public $error = null;
    public $fieldName;

    protected $listeners = ['fileSelected'];

    public function mount($oauthServiceId = null, $fieldName = 'url')
    {
        $this->oauthServiceId = $oauthServiceId;
        $this->fieldName = $fieldName;

        if ($this->oauthServiceId) {
            $this->loadFiles();
        }
    }

    public function updatedOauthServiceId()
    {
        $this->currentFolderId = 'root';
        $this->selectedFile = null;
        $this->selectedFileUrl = '';
        $this->searchQuery = '';
        $this->error = null;

        if ($this->oauthServiceId) {
            $this->loadFiles();
        } else {
            $this->files = [];
            $this->breadcrumb = [];
        }
    }

    public function loadFiles()
    {
        if (!$this->oauthServiceId) {
            return;
        }

        $this->isLoading = true;
        $this->error = null;

        try {
            $oauthService = OAuthService::find($this->oauthServiceId);

            if (!$oauthService || !$oauthService->is_active || !$oauthService->access_token) {
                throw new \Exception('OAuth service not configured or inactive');
            }

            $driveService = new GoogleDriveService($oauthService);

            // Load files
            $result = $driveService->listFiles($this->currentFolderId);
            $this->files = $result['files'];

            // Load breadcrumb
            $this->breadcrumb = $driveService->getBreadcrumb($this->currentFolderId);
        } catch (\Exception $e) {
            $this->error = 'Error loading files: ' . $e->getMessage();
            $this->files = [];
            $this->breadcrumb = [];
        }

        $this->isLoading = false;
    }

    public function navigateToFolder($folderId)
    {
        $this->currentFolderId = $folderId;
        $this->searchQuery = '';
        $this->loadFiles();
    }

    public function selectFile($file)
    {
        if ($file['isFolder']) {
            $this->navigateToFolder($file['id']);
        } else {
            $this->selectedFile = $file;
            $this->selectedFileUrl = $file['webViewLink'] ?? $file['webContentLink'] ?? '';

            // Emit event to update the form field
            $this->dispatch(
                'fileSelected',
                fieldName: $this->fieldName,
                fileUrl: $this->selectedFileUrl,
                fileName: $file['name']
            );
        }
    }

    public function search()
    {
        if (empty($this->searchQuery)) {
            $this->loadFiles();
            return;
        }

        if (!$this->oauthServiceId) {
            return;
        }

        $this->isLoading = true;
        $this->error = null;

        try {
            $oauthService = OAuthService::find($this->oauthServiceId);

            if (!$oauthService || !$oauthService->is_active || !$oauthService->access_token) {
                throw new \Exception('OAuth service not configured or inactive');
            }

            $driveService = new GoogleDriveService($oauthService);
            $this->files = $driveService->searchFiles($this->searchQuery, $this->currentFolderId);
        } catch (\Exception $e) {
            $this->error = 'Error searching files: ' . $e->getMessage();
            $this->files = [];
        }

        $this->isLoading = false;
    }

    public function clearSearch()
    {
        $this->searchQuery = '';
        $this->loadFiles();
    }

    public function getFileIcon($file)
    {
        if ($file['isFolder']) {
            return 'heroicon-o-folder';
        }

        $mimeType = $file['mimeType'];

        if (str_contains($mimeType, 'image/')) {
            return 'heroicon-o-photo';
        } elseif (str_contains($mimeType, 'video/')) {
            return 'heroicon-o-video-camera';
        } elseif (str_contains($mimeType, 'audio/')) {
            return 'heroicon-o-musical-note';
        } elseif (str_contains($mimeType, 'pdf')) {
            return 'heroicon-o-document-text';
        } elseif (str_contains($mimeType, 'spreadsheet') || str_contains($mimeType, 'excel')) {
            return 'heroicon-o-table-cells';
        } elseif (str_contains($mimeType, 'presentation') || str_contains($mimeType, 'powerpoint')) {
            return 'heroicon-o-presentation-chart-bar';
        } elseif (str_contains($mimeType, 'document') || str_contains($mimeType, 'word')) {
            return 'heroicon-o-document-text';
        } else {
            return 'heroicon-o-document';
        }
    }

    public function render()
    {
        return view('livewire.google-drive-explorer');
    }
}
